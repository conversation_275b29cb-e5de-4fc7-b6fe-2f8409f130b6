#!/usr/bin/env python3
"""
AutoGen APP开发工作流演示版本
不依赖AutoGen库，展示工作流的基本结构和逻辑
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, Any, List


class MockAgent:
    """模拟Agent类"""

    def __init__(self, name: str, role: str, system_message: str):
        self.name = name
        self.role = role
        self.system_message = system_message

    async def process_task(self, task: str, context: List[str] = None) -> str:
        """模拟处理任务"""
        await asyncio.sleep(1)  # 模拟处理时间

        if self.role == "coder":
            return self._generate_code_response(task)
        elif self.role == "reviewer":
            return self._generate_review_response(task, context)
        elif self.role == "optimizer":
            return self._generate_optimization_response(task, context)

        return f"[{self.name}] 处理完成: {task[:50]}..."

    def _generate_code_response(self, task: str) -> str:
        """生成代码编写响应"""
        return f"""
## 代码实现

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>待办事项管理</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .todo-input {{
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            margin-bottom: 15px;
        }}
        .todo-list {{
            list-style: none;
            padding: 0;
        }}
        .todo-item {{
            display: flex;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }}
        .todo-item.completed {{
            text-decoration: line-through;
            opacity: 0.6;
        }}
        .btn {{
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }}
        .btn-primary {{ background: #007bff; color: white; }}
        .btn-danger {{ background: #dc3545; color: white; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>待办事项管理</h1>
        <input type="text" class="todo-input" id="todoInput" placeholder="输入新的待办事项...">
        <button class="btn btn-primary" onclick="addTodo()">添加</button>
        <ul class="todo-list" id="todoList"></ul>
    </div>

    <script>
        let todos = JSON.parse(localStorage.getItem('todos')) || [];

        function renderTodos() {{
            const todoList = document.getElementById('todoList');
            todoList.innerHTML = '';

            todos.forEach((todo, index) => {{
                const li = document.createElement('li');
                li.className = `todo-item ${{todo.completed ? 'completed' : ''}}`;
                li.innerHTML = `
                    <input type="checkbox" ${{todo.completed ? 'checked' : ''}}
                           onchange="toggleTodo(${{index}})">
                    <span style="flex: 1; margin-left: 10px;">${{todo.text}}</span>
                    <button class="btn btn-danger" onclick="deleteTodo(${{index}})">删除</button>
                `;
                todoList.appendChild(li);
            }});
        }}

        function addTodo() {{
            const input = document.getElementById('todoInput');
            const text = input.value.trim();
            if (text) {{
                todos.push({{ text, completed: false }});
                input.value = '';
                saveTodos();
                renderTodos();
            }}
        }}

        function toggleTodo(index) {{
            todos[index].completed = !todos[index].completed;
            saveTodos();
            renderTodos();
        }}

        function deleteTodo(index) {{
            todos.splice(index, 1);
            saveTodos();
            renderTodos();
        }}

        function saveTodos() {{
            localStorage.setItem('todos', JSON.stringify(todos));
        }}

        // 初始化
        renderTodos();

        // 回车键添加
        document.getElementById('todoInput').addEventListener('keypress', function(e) {{
            if (e.key === 'Enter') {{
                addTodo();
            }}
        }});
    </script>
</body>
</html>
```

## 功能说明
1. 响应式设计，支持移动端
2. 本地存储，数据持久化
3. 添加、完成、删除功能
4. 简洁美观的界面设计

CODE_WRITTEN
"""

    def _generate_review_response(self, task: str, context: List[str]) -> str:
        """生成代码审查响应"""
        return f"""
## 代码审查报告

### 优点：
- ✅ 代码结构清晰，HTML、CSS、JavaScript分离良好
- ✅ 实现了所有基本功能：添加、完成、删除待办事项
- ✅ 使用localStorage实现数据持久化
- ✅ 响应式设计，支持移动端
- ✅ 代码注释充分，易于理解

### 问题和建议：
- ⚠️ **安全性问题** - 建议：对用户输入进行XSS防护，使用textContent而不是innerHTML
- ⚠️ **代码组织** - 建议：将JavaScript代码分离到独立文件，提高可维护性
- ⚠️ **错误处理** - 建议：添加localStorage访问失败的错误处理
- ⚠️ **用户体验** - 建议：添加空状态提示和操作确认
- ⚠️ **功能扩展** - 建议：考虑添加任务分类、优先级、截止日期等功能

### 总体评价：
代码实现了基本需求，结构合理，但在安全性和用户体验方面还有改进空间。建议按照上述建议进行优化。

REVIEW_COMPLETED
"""

    def _generate_optimization_response(self, task: str, context: List[str]) -> str:
        """生成代码优化响应"""
        return """
## 优化后的代码

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>待办事项管理</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>📝 待办事项管理</h1>
            <p class="subtitle">高效管理您的日常任务</p>
        </header>

        <div class="input-section">
            <input type="text" class="todo-input" id="todoInput"
                   placeholder="输入新的待办事项..." maxlength="100">
            <button class="btn btn-primary" id="addBtn">
                <span class="btn-icon">+</span> 添加
            </button>
        </div>

        <div class="stats">
            <span id="totalCount">总计: 0</span>
            <span id="completedCount">已完成: 0</span>
            <span id="pendingCount">待完成: 0</span>
        </div>

        <div class="todo-container">
            <ul class="todo-list" id="todoList"></ul>
            <div class="empty-state" id="emptyState">
                <p>🎉 暂无待办事项，添加一个开始吧！</p>
            </div>
        </div>

        <div class="actions">
            <button class="btn btn-secondary" onclick="clearCompleted()">清除已完成</button>
            <button class="btn btn-secondary" onclick="exportData()">导出数据</button>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
```

## 优化说明
- **安全性提升**：添加了XSS防护，使用textContent和escapeHtml方法
- **代码组织**：将CSS和JavaScript分离到独立文件，提高可维护性
- **错误处理**：添加了localStorage访问失败的错误处理
- **用户体验**：添加了空状态提示、操作确认、消息提示
- **功能扩展**：添加了统计信息、清除已完成、数据导出功能
- **响应式设计**：优化了移动端体验
- **代码质量**：使用ES6类、模块化设计、更好的命名规范

## 总结
优化后的代码在安全性、可维护性、用户体验等方面都有显著提升，同时保持了原有功能的完整性。

WORKFLOW_COMPLETE
"""


class DemoWorkflow:
    """演示工作流类"""

    def __init__(self):
        self.agents = {
            "coder": MockAgent("coder", "coder", "专业的代码编写工程师"),
            "reviewer": MockAgent("reviewer", "reviewer", "资深的代码审查专家"),
            "optimizer": MockAgent("optimizer", "optimizer", "代码优化专家")
        }
        self.workflow_history = []

    async def run_workflow(self, requirement: str, app_type: str = "web") -> Dict[str, Any]:
        """运行演示工作流"""
        start_time = datetime.now()

        print(f"🚀 开始执行APP开发工作流")
        print(f"📋 需求: {requirement}")
        print(f"🎯 应用类型: {app_type}")
        print("=" * 60)

        try:
            # 阶段1: Coder编写代码
            print("\n🧑‍💻 阶段1: 代码编写")
            print("-" * 30)
            coder_response = await self.agents["coder"].process_task(requirement)
            print(f"✅ Coder完成代码编写")
            self.workflow_history.append(("coder", coder_response))

            # 阶段2: Reviewer审查代码
            print("\n🔍 阶段2: 代码审查")
            print("-" * 30)
            reviewer_response = await self.agents["reviewer"].process_task(
                requirement, [coder_response]
            )
            print(f"✅ Reviewer完成代码审查")
            self.workflow_history.append(("reviewer", reviewer_response))

            # 阶段3: Optimizer优化代码
            print("\n⚡ 阶段3: 代码优化")
            print("-" * 30)
            optimizer_response = await self.agents["optimizer"].process_task(
                requirement, [coder_response, reviewer_response]
            )
            print(f"✅ Optimizer完成代码优化")
            self.workflow_history.append(("optimizer", optimizer_response))

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            result = {
                "success": True,
                "start_time": start_time,
                "end_time": end_time,
                "duration": duration,
                "requirement": requirement,
                "app_type": app_type,
                "workflow_history": self.workflow_history
            }

            print(f"\n🎉 工作流执行完成！")
            print(f"⏱️  总耗时: {duration:.2f}秒")

            return result

        except Exception as e:
            print(f"❌ 工作流执行失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "start_time": start_time,
                "end_time": datetime.now()
            }

    def save_result(self, result: Dict[str, Any], filename: str = None):
        """保存工作流结果"""
        if not filename:
            timestamp = result["start_time"].strftime("%Y%m%d_%H%M%S")
            filename = f"demo_workflow_result_{timestamp}.json"

        # 准备保存的数据
        save_data = {
            "success": result["success"],
            "start_time": result["start_time"].isoformat(),
            "end_time": result["end_time"].isoformat(),
            "duration": result["duration"],
            "requirement": result["requirement"],
            "app_type": result["app_type"],
            "workflow_steps": len(result.get("workflow_history", [])),
            "final_output": result.get("workflow_history", [])[-1][1] if result.get("workflow_history") else None
        }

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)

        print(f"📄 结果已保存到: {filename}")


async def main():
    """主函数演示"""
    print("🎬 AutoGen APP开发工作流演示")
    print("=" * 50)

    # 创建演示工作流
    demo = DemoWorkflow()

    # 示例需求
    requirement = """
开发一个现代化的待办事项管理Web应用，具体要求：

功能需求：
1. 添加、编辑、删除待办事项
2. 标记事项完成状态
3. 任务分类和筛选
4. 数据本地存储
5. 响应式设计
6. 简洁美观的用户界面

技术要求：
- 使用现代前端技术
- 组件化开发
- 良好的用户体验
- 代码规范和注释
"""

    # 运行工作流
    result = await demo.run_workflow(requirement, "web")

    # 保存结果
    if result["success"]:
        demo.save_result(result)

        print("\n📋 工作流总结:")
        print(f"- 执行状态: {'✅ 成功' if result['success'] else '❌ 失败'}")
        print(f"- 执行时间: {result['duration']:.2f}秒")
        print(f"- 工作流步骤: {len(result.get('workflow_history', []))}个")
        print(f"- 应用类型: {result['app_type']}")

    print("\n🏁 演示完成")


if __name__ == "__main__":
    asyncio.run(main())