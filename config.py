"""
AutoGen APP开发工作流配置文件
"""

import os
from typing import Dict, Any

class WorkflowConfig:
    """工作流配置管理"""
    
    # 支持的模型列表
    SUPPORTED_MODELS = [
        "gpt-4o",
        "gpt-4o-mini", 
        "gpt-4-turbo",
        "gpt-3.5-turbo"
    ]
    
    # 支持的应用类型
    APP_TYPES = {
        "web": {
            "name": "Web应用",
            "description": "基于浏览器的Web应用程序",
            "technologies": ["HTML", "CSS", "JavaScript", "React", "Vue", "Angular"]
        },
        "mobile": {
            "name": "移动应用", 
            "description": "iOS和Android移动应用",
            "technologies": ["React Native", "Flutter", "Swift", "Kotlin", "Java"]
        },
        "desktop": {
            "name": "桌面应用",
            "description": "Windows、macOS、Linux桌面应用",
            "technologies": ["Electron", "PyQt", "Tkinter", "WPF", "JavaFX"]
        },
        "api": {
            "name": "API服务",
            "description": "后端API和微服务",
            "technologies": ["FastAPI", "Flask", "Django", "Express.js", "Spring Boot"]
        }
    }
    
    # 默认配置
    DEFAULT_CONFIG = {
        "model_name": "gpt-4o",
        "max_tokens": 4000,
        "temperature": 0.7,
        "timeout": 300,  # 5分钟超时
        "save_output": True,
        "output_format": "markdown"
    }
    
    # Agent角色配置
    AGENT_ROLES = {
        "coder": {
            "name": "代码编写者",
            "description": "负责根据需求编写高质量代码",
            "expertise": ["编程", "架构设计", "最佳实践"],
            "output_keywords": ["CODE_WRITTEN"]
        },
        "reviewer": {
            "name": "代码审查者", 
            "description": "负责审查代码质量并提出改进建议",
            "expertise": ["代码审查", "质量保证", "安全性检查"],
            "output_keywords": ["REVIEW_COMPLETED"]
        },
        "optimizer": {
            "name": "代码优化者",
            "description": "负责根据审查建议优化代码",
            "expertise": ["性能优化", "代码重构", "最佳实践"],
            "output_keywords": ["WORKFLOW_COMPLETE"]
        }
    }
    
    # 工作流模板
    WORKFLOW_TEMPLATES = {
        "basic": {
            "name": "基础开发流程",
            "description": "Coder -> Reviewer -> Optimizer",
            "agents": ["coder", "reviewer", "optimizer"],
            "flow": "sequential"
        },
        "iterative": {
            "name": "迭代开发流程", 
            "description": "支持多轮迭代的开发流程",
            "agents": ["coder", "reviewer", "optimizer"],
            "flow": "iterative",
            "max_iterations": 3
        }
    }
    
    @classmethod
    def get_config(cls, config_name: str = "default") -> Dict[str, Any]:
        """获取配置"""
        if config_name == "default":
            return cls.DEFAULT_CONFIG.copy()
        return {}
    
    @classmethod
    def get_app_type_info(cls, app_type: str) -> Dict[str, Any]:
        """获取应用类型信息"""
        return cls.APP_TYPES.get(app_type, {})
    
    @classmethod
    def get_agent_role(cls, role: str) -> Dict[str, Any]:
        """获取Agent角色信息"""
        return cls.AGENT_ROLES.get(role, {})
    
    @classmethod
    def validate_model(cls, model_name: str) -> bool:
        """验证模型是否支持"""
        return model_name in cls.SUPPORTED_MODELS
    
    @classmethod
    def validate_app_type(cls, app_type: str) -> bool:
        """验证应用类型是否支持"""
        return app_type in cls.APP_TYPES


# 环境变量配置
class EnvConfig:
    """环境变量配置"""
    
    @staticmethod
    def get_openai_api_key() -> str:
        """获取OpenAI API密钥"""
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("请设置OPENAI_API_KEY环境变量")
        return api_key
    
    @staticmethod
    def get_output_dir() -> str:
        """获取输出目录"""
        return os.getenv("WORKFLOW_OUTPUT_DIR", "./output")
    
    @staticmethod
    def get_log_level() -> str:
        """获取日志级别"""
        return os.getenv("LOG_LEVEL", "INFO")


# 提示词模板
class PromptTemplates:
    """提示词模板"""
    
    CODER_SYSTEM_TEMPLATE = """你是一个专业的{app_type}应用开发工程师。你的职责是：

1. 根据用户需求编写高质量的代码
2. 遵循{app_type}开发的最佳实践和编码规范
3. 使用推荐的技术栈：{technologies}
4. 确保代码的可读性和可维护性
5. 包含必要的注释和文档
6. 考虑性能和安全性

请按照以下格式输出代码：
```[语言]
[代码内容]
```

在代码后添加简要说明，解释主要功能和设计思路。
完成后请说"CODE_WRITTEN"。
"""
    
    REVIEWER_SYSTEM_TEMPLATE = """你是一个资深的{app_type}代码审查专家。你的职责是：

1. 仔细审查提供的代码
2. 检查代码质量、安全性、性能问题
3. 验证是否符合{app_type}开发最佳实践
4. 检查代码逻辑和潜在bug
5. 提出具体的改进建议

请按照以下格式提供审查报告：

## 代码审查报告

### 优点：
- [列出代码的优点]

### 问题和建议：
- [具体问题1] - 建议：[改进方案]
- [具体问题2] - 建议：[改进方案]

### 总体评价：
[整体评价和建议]

完成审查后请说"REVIEW_COMPLETED"。
"""
    
    OPTIMIZER_SYSTEM_TEMPLATE = """你是一个{app_type}代码优化专家。你的职责是：

1. 分析原始代码和审查建议
2. 根据审查意见优化代码
3. 提高代码质量、性能和可维护性
4. 确保优化后的代码更加完善
5. 保持原有功能的完整性

请按照以下格式输出优化后的代码：

## 优化后的代码

```[语言]
[优化后的代码内容]
```

## 优化说明
- [优化点1]：[具体改进]
- [优化点2]：[具体改进]

## 总结
[优化总结和最终建议]

完成优化后请说"WORKFLOW_COMPLETE"。
"""
    
    @classmethod
    def get_coder_prompt(cls, app_type: str, technologies: list) -> str:
        """获取Coder的系统提示词"""
        tech_str = ", ".join(technologies)
        return cls.CODER_SYSTEM_TEMPLATE.format(
            app_type=app_type,
            technologies=tech_str
        )
    
    @classmethod
    def get_reviewer_prompt(cls, app_type: str) -> str:
        """获取Reviewer的系统提示词"""
        return cls.REVIEWER_SYSTEM_TEMPLATE.format(app_type=app_type)
    
    @classmethod
    def get_optimizer_prompt(cls, app_type: str) -> str:
        """获取Optimizer的系统提示词"""
        return cls.OPTIMIZER_SYSTEM_TEMPLATE.format(app_type=app_type)
