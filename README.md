# AutoGen APP开发工作流

基于Microsoft AutoGen框架的智能APP开发工作流，实现三个AI Agent协作完成应用开发任务。

## 🌟 特性

- **三Agent协作**: Coder(编写) → Reviewer(审查) → Optimizer(优化)
- **多应用类型支持**: Web、Mobile、Desktop、API
- **智能工作流**: 基于AutoGen GraphFlow的有向图工作流
- **配置化管理**: 灵活的配置和模板系统
- **命令行界面**: 支持交互式和批处理模式
- **结果保存**: 自动保存工作流结果为JSON和Markdown格式

## 🏗️ 架构设计

```mermaid
graph LR
    A[用户需求] --> B[Coder Agent]
    B --> C[Reviewer Agent]
    C --> D[Optimizer Agent]
    D --> E[最终代码]
    
    B -.-> F[编写代码]
    C -.-> G[审查建议]
    D -.-> H[优化代码]
```

### Agent角色

1. **Coder Agent** 🧑‍💻
   - 根据需求编写高质量代码
   - 遵循最佳实践和编码规范
   - 考虑性能和安全性

2. **Reviewer Agent** 🔍
   - 审查代码质量和安全性
   - 检查潜在bug和问题
   - 提出具体改进建议

3. **Optimizer Agent** ⚡
   - 根据审查建议优化代码
   - 提高代码质量和性能
   - 确保功能完整性

## 📦 安装

### 环境要求

- Python 3.8+
- OpenAI API Key

### 安装依赖

```bash
pip install autogen-agentchat autogen-ext[openai]
```

### 环境配置

设置OpenAI API密钥：

```bash
export OPENAI_API_KEY="your-api-key-here"
```

或创建 `.env` 文件：

```
OPENAI_API_KEY=your-api-key-here
WORKFLOW_OUTPUT_DIR=./output
LOG_LEVEL=INFO
```

## 🚀 快速开始

### 1. 基础使用

```python
import asyncio
from enhanced_workflow import EnhancedAppDevWorkflow

async def main():
    workflow = EnhancedAppDevWorkflow()
    
    result = await workflow.run_workflow(
        requirement="开发一个待办事项管理应用",
        app_type="web"
    )
    
    print(f"执行结果: {result['success']}")

asyncio.run(main())
```

### 2. 命令行使用

```bash
# 基本用法
python cli.py --requirement "开发一个用户登录系统" --app-type api

# 交互式模式
python cli.py --interactive

# 指定模型
python cli.py --requirement "创建移动应用" --app-type mobile --model gpt-4o-mini

# 查看支持的类型
python cli.py --list-types
python cli.py --list-models
```

### 3. 交互式模式

```bash
python cli.py -i
```

按提示选择应用类型、模型，并输入需求描述。

## 📋 支持的应用类型

| 类型 | 描述 | 技术栈 |
|------|------|--------|
| `web` | Web应用 | HTML, CSS, JavaScript, React, Vue |
| `mobile` | 移动应用 | React Native, Flutter, Swift, Kotlin |
| `desktop` | 桌面应用 | Electron, PyQt, Tkinter, WPF |
| `api` | API服务 | FastAPI, Flask, Django, Express.js |

## 🤖 支持的模型

- `gpt-4o` (推荐)
- `gpt-4o-mini`
- `gpt-4-turbo`
- `gpt-3.5-turbo`

## 📁 项目结构

```
autogen-app-workflow/
├── app_dev_workflow.py      # 基础工作流实现
├── enhanced_workflow.py     # 增强版工作流
├── config.py               # 配置管理
├── cli.py                  # 命令行界面
├── README.md               # 项目文档
├── requirements.txt        # 依赖列表
├── examples/               # 示例文件
│   ├── web_app_example.py
│   └── api_example.py
└── output/                 # 输出目录
    ├── workflow_result_*.json
    └── workflow_result_*.md
```

## 🔧 配置选项

### 基本配置

```python
config = {
    "model_name": "gpt-4o",
    "max_tokens": 4000,
    "temperature": 0.7,
    "timeout": 300,
    "save_output": True,
    "output_format": "markdown"
}
```

### 自定义Agent提示词

可以通过修改 `config.py` 中的 `PromptTemplates` 类来自定义Agent的行为。

## 📊 输出格式

工作流执行完成后会生成两种格式的结果：

### JSON格式 (详细数据)
```json
{
  "success": true,
  "start_time": "2024-01-01T10:00:00",
  "duration": 45.2,
  "requirement": "开发需求...",
  "app_type": "web",
  "config": {...}
}
```

### Markdown格式 (可读报告)
```markdown
# AutoGen APP开发工作流报告

## 基本信息
- 执行时间: 2024-01-01 10:00:00
- 应用类型: web
- 状态: ✅ 成功

## 需求描述
...

## 执行结果
...
```

## 🎯 使用示例

### Web应用开发

```bash
python cli.py --requirement "
开发一个博客系统，包括：
1. 文章发布和编辑
2. 用户评论功能
3. 标签分类
4. 响应式设计
" --app-type web
```

### API服务开发

```bash
python cli.py --requirement "
创建一个电商API，包括：
1. 商品管理接口
2. 订单处理接口
3. 用户认证
4. 支付集成
" --app-type api
```

## 🔍 故障排除

### 常见问题

1. **API密钥错误**
   ```
   错误: 请设置OPENAI_API_KEY环境变量
   解决: 确保正确设置了OpenAI API密钥
   ```

2. **模型不支持**
   ```
   错误: 不支持的模型: xxx
   解决: 使用 --list-models 查看支持的模型列表
   ```

3. **网络连接问题**
   ```
   错误: 连接超时
   解决: 检查网络连接和API服务状态
   ```

### 日志查看

工作流执行日志保存在 `workflow.log` 文件中：

```bash
tail -f workflow.log
```

## 🤝 贡献

欢迎提交Issue和Pull Request！

### 开发环境设置

```bash
git clone <repository>
cd autogen-app-workflow
pip install -r requirements.txt
```

### 运行测试

```bash
python -m pytest tests/
```

## 📄 许可证

MIT License

## 🙏 致谢

- [Microsoft AutoGen](https://github.com/microsoft/autogen) - 强大的多Agent框架
- OpenAI - 提供优秀的语言模型

---

**注意**: 本项目需要OpenAI API密钥，使用前请确保已正确配置。
