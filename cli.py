#!/usr/bin/env python3
"""
AutoGen APP开发工作流命令行界面
"""

import asyncio
import argparse
import sys
from pathlib import Path
from typing import Dict, Any

from enhanced_workflow import EnhancedAppDevWorkflow
from config import WorkflowConfig


class WorkflowCLI:
    """工作流命令行界面"""
    
    def __init__(self):
        self.workflow = None
    
    def create_parser(self) -> argparse.ArgumentParser:
        """创建命令行参数解析器"""
        parser = argparse.ArgumentParser(
            description="AutoGen APP开发工作流",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
示例用法:
  python cli.py --requirement "开发一个待办事项应用" --app-type web
  python cli.py --requirement "创建用户登录API" --app-type api --model gpt-4o-mini
  python cli.py --interactive
            """
        )
        
        # 基本参数
        parser.add_argument(
            "--requirement", "-r",
            type=str,
            help="应用开发需求描述"
        )
        
        parser.add_argument(
            "--app-type", "-t",
            choices=list(WorkflowConfig.APP_TYPES.keys()),
            default="web",
            help="应用类型 (默认: web)"
        )
        
        parser.add_argument(
            "--model", "-m",
            choices=WorkflowConfig.SUPPORTED_MODELS,
            default="gpt-4o",
            help="使用的模型 (默认: gpt-4o)"
        )
        
        parser.add_argument(
            "--output-dir", "-o",
            type=str,
            default="./output",
            help="输出目录 (默认: ./output)"
        )
        
        parser.add_argument(
            "--no-save",
            action="store_true",
            help="不保存输出结果"
        )
        
        parser.add_argument(
            "--interactive", "-i",
            action="store_true",
            help="交互式模式"
        )
        
        parser.add_argument(
            "--list-types",
            action="store_true",
            help="列出支持的应用类型"
        )
        
        parser.add_argument(
            "--list-models",
            action="store_true",
            help="列出支持的模型"
        )
        
        return parser
    
    def list_app_types(self):
        """列出支持的应用类型"""
        print("支持的应用类型:")
        print("-" * 50)
        for key, info in WorkflowConfig.APP_TYPES.items():
            print(f"  {key:10} - {info['name']}")
            print(f"             {info['description']}")
            print(f"             技术栈: {', '.join(info['technologies'][:3])}...")
            print()
    
    def list_models(self):
        """列出支持的模型"""
        print("支持的模型:")
        print("-" * 30)
        for model in WorkflowConfig.SUPPORTED_MODELS:
            print(f"  - {model}")
        print()
    
    async def interactive_mode(self):
        """交互式模式"""
        print("🚀 AutoGen APP开发工作流 - 交互式模式")
        print("=" * 50)
        
        # 选择应用类型
        print("\n📱 选择应用类型:")
        for i, (key, info) in enumerate(WorkflowConfig.APP_TYPES.items(), 1):
            print(f"  {i}. {info['name']} ({key})")
        
        while True:
            try:
                choice = input("\n请选择应用类型 (1-4): ").strip()
                app_types = list(WorkflowConfig.APP_TYPES.keys())
                app_type = app_types[int(choice) - 1]
                break
            except (ValueError, IndexError):
                print("❌ 无效选择，请重新输入")
        
        # 选择模型
        print(f"\n🤖 选择模型 (默认: gpt-4o):")
        for i, model in enumerate(WorkflowConfig.SUPPORTED_MODELS, 1):
            print(f"  {i}. {model}")
        
        model_choice = input("\n请选择模型 (直接回车使用默认): ").strip()
        if model_choice:
            try:
                model = WorkflowConfig.SUPPORTED_MODELS[int(model_choice) - 1]
            except (ValueError, IndexError):
                print("❌ 无效选择，使用默认模型")
                model = "gpt-4o"
        else:
            model = "gpt-4o"
        
        # 输入需求
        print(f"\n📝 请输入应用开发需求:")
        print("(可以多行输入，输入'END'结束)")
        
        requirement_lines = []
        while True:
            line = input()
            if line.strip().upper() == "END":
                break
            requirement_lines.append(line)
        
        requirement = "\n".join(requirement_lines)
        
        if not requirement.strip():
            print("❌ 需求不能为空")
            return
        
        # 确认信息
        print(f"\n📋 确认信息:")
        print(f"  应用类型: {WorkflowConfig.APP_TYPES[app_type]['name']} ({app_type})")
        print(f"  模型: {model}")
        print(f"  需求: {requirement[:100]}...")
        
        confirm = input("\n确认执行? (y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ 已取消")
            return
        
        # 执行工作流
        await self.run_workflow(requirement, app_type, model)
    
    async def run_workflow(self, requirement: str, app_type: str, model: str, save_output: bool = True):
        """运行工作流"""
        try:
            # 创建配置
            config = WorkflowConfig.get_config()
            config["model_name"] = model
            config["save_output"] = save_output
            
            # 创建工作流实例
            self.workflow = EnhancedAppDevWorkflow(config)
            
            print(f"\n🚀 开始执行工作流...")
            print(f"📋 需求: {requirement[:50]}...")
            print(f"🎯 应用类型: {app_type}")
            print(f"🤖 模型: {model}")
            print("=" * 60)
            
            # 运行工作流
            result = await self.workflow.run_workflow(
                requirement=requirement,
                app_type=app_type
            )
            
            # 显示结果
            if result["success"]:
                print(f"\n✅ 工作流执行成功!")
                print(f"⏱️  执行时间: {result['duration']:.2f}秒")
                if save_output:
                    print(f"📄 结果已保存到 output 目录")
            else:
                print(f"\n❌ 工作流执行失败!")
                print(f"🔍 错误信息: {result.get('error', '未知错误')}")
            
        except Exception as e:
            print(f"\n❌ 执行出错: {str(e)}")
        finally:
            if self.workflow:
                await self.workflow.cleanup()
    
    async def main(self):
        """主函数"""
        parser = self.create_parser()
        args = parser.parse_args()
        
        # 处理列表命令
        if args.list_types:
            self.list_app_types()
            return
        
        if args.list_models:
            self.list_models()
            return
        
        # 交互式模式
        if args.interactive:
            await self.interactive_mode()
            return
        
        # 检查必需参数
        if not args.requirement:
            print("❌ 错误: 需要提供 --requirement 参数或使用 --interactive 模式")
            parser.print_help()
            sys.exit(1)
        
        # 运行工作流
        await self.run_workflow(
            requirement=args.requirement,
            app_type=args.app_type,
            model=args.model,
            save_output=not args.no_save
        )


def main():
    """入口函数"""
    cli = WorkflowCLI()
    asyncio.run(cli.main())


if __name__ == "__main__":
    main()
