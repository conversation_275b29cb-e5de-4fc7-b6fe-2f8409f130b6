#!/usr/bin/env python3
"""
增强版AutoGen APP开发工作流
集成配置管理、错误处理、日志记录等功能
"""

import asyncio
import os
import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List
from pathlib import Path

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.teams import DiGraphBuilder, GraphFlow
from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient

from config import WorkflowConfig, EnvConfig, PromptTemplates


class EnhancedAppDevWorkflow:
    """增强版APP开发工作流管理器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化增强版工作流
        
        Args:
            config: 自定义配置，如果为None则使用默认配置
        """
        # 加载配置
        self.config = config or WorkflowConfig.get_config()
        
        # 设置日志
        self._setup_logging()
        
        # 验证配置
        self._validate_config()
        
        # 创建输出目录
        self.output_dir = Path(EnvConfig.get_output_dir())
        self.output_dir.mkdir(exist_ok=True)
        
        # 初始化模型客户端
        self.model_client = OpenAIChatCompletionClient(
            model=self.config["model_name"],
            api_key=EnvConfig.get_openai_api_key()
        )
        
        # 初始化agents和工作流
        self.agents = {}
        self.workflow = None
        
        self.logger.info(f"工作流初始化完成，使用模型: {self.config['model_name']}")
    
    def _setup_logging(self):
        """设置日志"""
        log_level = getattr(logging, EnvConfig.get_log_level().upper())
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('workflow.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def _validate_config(self):
        """验证配置"""
        model_name = self.config.get("model_name")
        if not WorkflowConfig.validate_model(model_name):
            raise ValueError(f"不支持的模型: {model_name}")
        
        self.logger.info("配置验证通过")
    
    def _create_agents(self, app_type: str):
        """创建三个专门的agents"""
        if not WorkflowConfig.validate_app_type(app_type):
            raise ValueError(f"不支持的应用类型: {app_type}")
        
        app_info = WorkflowConfig.get_app_type_info(app_type)
        technologies = app_info.get("technologies", [])
        
        # 创建Coder Agent
        coder_prompt = PromptTemplates.get_coder_prompt(app_type, technologies)
        self.agents["coder"] = AssistantAgent(
            name="coder",
            model_client=self.model_client,
            system_message=coder_prompt,
            description=WorkflowConfig.get_agent_role("coder")["description"]
        )
        
        # 创建Reviewer Agent
        reviewer_prompt = PromptTemplates.get_reviewer_prompt(app_type)
        self.agents["reviewer"] = AssistantAgent(
            name="reviewer",
            model_client=self.model_client,
            system_message=reviewer_prompt,
            description=WorkflowConfig.get_agent_role("reviewer")["description"]
        )
        
        # 创建Optimizer Agent
        optimizer_prompt = PromptTemplates.get_optimizer_prompt(app_type)
        self.agents["optimizer"] = AssistantAgent(
            name="optimizer",
            model_client=self.model_client,
            system_message=optimizer_prompt,
            description=WorkflowConfig.get_agent_role("optimizer")["description"]
        )
        
        self.logger.info(f"已创建{len(self.agents)}个agents")
    
    def _create_workflow(self):
        """创建工作流图"""
        # 使用DiGraphBuilder创建有向图
        builder = DiGraphBuilder()
        
        # 添加节点
        for agent in self.agents.values():
            builder.add_node(agent)
        
        # 定义工作流：Coder -> Reviewer -> Optimizer
        builder.add_edge(self.agents["coder"], self.agents["reviewer"])
        builder.add_edge(self.agents["reviewer"], self.agents["optimizer"])
        
        # 构建图
        graph = builder.build()
        
        # 创建终止条件
        text_termination = TextMentionTermination("WORKFLOW_COMPLETE")
        max_message_termination = MaxMessageTermination(max_messages=20)
        termination = text_termination | max_message_termination
        
        # 创建工作流
        self.workflow = GraphFlow(
            participants=list(self.agents.values()),
            graph=graph
        )
        
        self.logger.info("工作流图创建完成")
    
    async def run_workflow(self, 
                          requirement: str, 
                          app_type: str = "web",
                          custom_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        运行APP开发工作流
        
        Args:
            requirement: 应用开发需求描述
            app_type: 应用类型
            custom_config: 自定义配置
            
        Returns:
            包含执行结果的字典
        """
        start_time = datetime.now()
        
        self.logger.info(f"开始执行工作流 - 需求: {requirement[:50]}...")
        
        try:
            # 应用自定义配置
            if custom_config:
                self.config.update(custom_config)
            
            # 创建agents和工作流
            self._create_agents(app_type)
            self._create_workflow()
            
            # 构建任务描述
            app_info = WorkflowConfig.get_app_type_info(app_type)
            task = self._build_task_description(requirement, app_type, app_info)
            
            # 运行工作流
            self.logger.info("开始执行工作流...")
            
            # 收集所有消息
            messages = []
            async for message in self.workflow.run_stream(task=task):
                messages.append(message)
                # 实时显示到控制台
                print(f"[{message.source if hasattr(message, 'source') else 'System'}] {str(message)[:100]}...")
            
            # 构建结果
            result = {
                "success": True,
                "start_time": start_time,
                "end_time": datetime.now(),
                "duration": (datetime.now() - start_time).total_seconds(),
                "requirement": requirement,
                "app_type": app_type,
                "messages": messages,
                "config": self.config
            }
            
            # 保存结果
            if self.config.get("save_output", True):
                await self._save_workflow_result(result)
            
            self.logger.info(f"工作流执行完成，耗时: {result['duration']:.2f}秒")
            return result
            
        except Exception as e:
            self.logger.error(f"工作流执行失败: {str(e)}")
            result = {
                "success": False,
                "error": str(e),
                "start_time": start_time,
                "end_time": datetime.now(),
                "requirement": requirement,
                "app_type": app_type
            }
            return result
        
        finally:
            await self.cleanup()
    
    def _build_task_description(self, requirement: str, app_type: str, app_info: Dict[str, Any]) -> str:
        """构建任务描述"""
        technologies = ", ".join(app_info.get("technologies", []))
        
        task = f"""
请开发一个{app_info.get('name', app_type)}，具体需求如下：

## 需求描述
{requirement}

## 技术要求
- 应用类型: {app_info.get('name', app_type)}
- 推荐技术栈: {technologies}
- 代码质量: 高质量、可维护、有注释

## 工作流程
1. **Coder**: 根据需求编写代码实现，完成后说"CODE_WRITTEN"
2. **Reviewer**: 审查代码质量并提出改进建议，完成后说"REVIEW_COMPLETED"  
3. **Optimizer**: 根据审查建议优化代码，完成后说"WORKFLOW_COMPLETE"

请开始工作流程！
"""
        return task
    
    async def _save_workflow_result(self, result: Dict[str, Any]):
        """保存工作流结果"""
        timestamp = result["start_time"].strftime("%Y%m%d_%H%M%S")
        
        # 保存详细结果（JSON格式）
        json_filename = self.output_dir / f"workflow_result_{timestamp}.json"
        
        # 准备JSON数据（移除不可序列化的对象）
        json_data = {
            "success": result["success"],
            "start_time": result["start_time"].isoformat(),
            "end_time": result["end_time"].isoformat(),
            "duration": result["duration"],
            "requirement": result["requirement"],
            "app_type": result["app_type"],
            "config": result["config"],
            "message_count": len(result.get("messages", []))
        }
        
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
        
        # 保存可读性结果（Markdown格式）
        md_filename = self.output_dir / f"workflow_result_{timestamp}.md"
        md_content = self._generate_markdown_report(result)
        
        with open(md_filename, 'w', encoding='utf-8') as f:
            f.write(md_content)
        
        self.logger.info(f"结果已保存到: {json_filename} 和 {md_filename}")
    
    def _generate_markdown_report(self, result: Dict[str, Any]) -> str:
        """生成Markdown格式的报告"""
        content = f"""# AutoGen APP开发工作流报告

## 基本信息
- **执行时间**: {result['start_time'].strftime('%Y-%m-%d %H:%M:%S')}
- **执行时长**: {result['duration']:.2f}秒
- **应用类型**: {result['app_type']}
- **模型**: {result['config']['model_name']}
- **状态**: {'✅ 成功' if result['success'] else '❌ 失败'}

## 需求描述
{result['requirement']}

## 执行结果
"""
        
        if result["success"]:
            messages = result.get("messages", [])
            content += f"- 总消息数: {len(messages)}\n"
            content += "- 工作流按预期完成\n"
        else:
            content += f"- 错误信息: {result.get('error', '未知错误')}\n"
        
        content += f"""
## 配置信息
```json
{json.dumps(result['config'], ensure_ascii=False, indent=2)}
```

---
*由AutoGen增强版APP开发工作流自动生成*
"""
        
        return content
    
    async def cleanup(self):
        """清理资源"""
        if self.model_client:
            await self.model_client.close()
        self.logger.info("资源清理完成")


async def main():
    """主函数示例"""
    # 示例需求
    requirements = [
        {
            "description": "开发一个简单的待办事项管理Web应用",
            "details": """
功能需求：
1. 添加新的待办事项
2. 标记事项为完成状态
3. 删除事项
4. 显示所有事项列表
5. 简洁美观的用户界面
6. 数据持久化存储
""",
            "app_type": "web"
        },
        {
            "description": "开发一个用户注册登录API服务",
            "details": """
功能需求：
1. 用户注册接口
2. 用户登录接口
3. JWT token验证
4. 密码加密存储
5. 用户信息管理
6. RESTful API设计
""",
            "app_type": "api"
        }
    ]
    
    try:
        # 创建工作流实例
        workflow = EnhancedAppDevWorkflow()
        
        # 运行多个示例
        for i, req in enumerate(requirements, 1):
            print(f"\n{'='*60}")
            print(f"执行示例 {i}: {req['description']}")
            print(f"{'='*60}")
            
            result = await workflow.run_workflow(
                requirement=req["details"],
                app_type=req["app_type"]
            )
            
            if result["success"]:
                print(f"✅ 示例 {i} 执行成功")
            else:
                print(f"❌ 示例 {i} 执行失败: {result.get('error')}")
        
    except Exception as e:
        print(f"执行失败: {str(e)}")


if __name__ == "__main__":
    # 运行主函数
    asyncio.run(main())
