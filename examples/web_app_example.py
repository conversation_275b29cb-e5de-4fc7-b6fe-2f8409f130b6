#!/usr/bin/env python3
"""
Web应用开发示例
演示如何使用AutoGen工作流开发Web应用
"""

import asyncio
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from enhanced_workflow import EnhancedAppDevWorkflow


async def develop_todo_app():
    """开发待办事项应用示例"""
    
    requirement = """
开发一个现代化的待办事项管理Web应用，具体要求：

## 功能需求
1. **任务管理**
   - 添加新任务
   - 编辑任务内容
   - 标记任务完成/未完成
   - 删除任务

2. **任务分类**
   - 支持任务分类（工作、生活、学习等）
   - 按分类筛选任务
   - 分类颜色标识

3. **用户界面**
   - 响应式设计，支持移动端
   - 现代化UI设计
   - 流畅的交互动画
   - 暗色/亮色主题切换

4. **数据持久化**
   - 本地存储支持
   - 数据导入/导出功能

## 技术要求
- 使用现代前端框架（React/Vue）
- 组件化开发
- 状态管理
- CSS预处理器
- 代码规范和注释
"""
    
    print("🚀 开始开发待办事项Web应用")
    print("=" * 60)
    
    try:
        # 创建工作流实例
        workflow = EnhancedAppDevWorkflow()
        
        # 运行工作流
        result = await workflow.run_workflow(
            requirement=requirement,
            app_type="web"
        )
        
        if result["success"]:
            print("✅ 待办事项应用开发完成！")
            print(f"⏱️  执行时间: {result['duration']:.2f}秒")
        else:
            print(f"❌ 开发失败: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ 执行出错: {str(e)}")


async def develop_blog_system():
    """开发博客系统示例"""
    
    requirement = """
开发一个功能完整的博客系统，具体要求：

## 功能需求
1. **文章管理**
   - 文章发布和编辑
   - Markdown支持
   - 文章分类和标签
   - 文章搜索功能

2. **用户系统**
   - 用户注册/登录
   - 用户资料管理
   - 权限控制（管理员/普通用户）

3. **互动功能**
   - 文章评论
   - 点赞功能
   - 分享功能

4. **管理后台**
   - 文章管理
   - 用户管理
   - 评论管理
   - 数据统计

## 技术要求
- 前后端分离架构
- RESTful API设计
- 数据库设计
- 安全性考虑
- SEO优化
"""
    
    print("🚀 开始开发博客系统")
    print("=" * 60)
    
    try:
        # 创建工作流实例
        workflow = EnhancedAppDevWorkflow()
        
        # 运行工作流
        result = await workflow.run_workflow(
            requirement=requirement,
            app_type="web"
        )
        
        if result["success"]:
            print("✅ 博客系统开发完成！")
            print(f"⏱️  执行时间: {result['duration']:.2f}秒")
        else:
            print(f"❌ 开发失败: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ 执行出错: {str(e)}")


async def main():
    """主函数"""
    print("AutoGen Web应用开发示例")
    print("=" * 40)
    print("1. 待办事项应用")
    print("2. 博客系统")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择示例 (1-3): ").strip()
        
        if choice == "1":
            await develop_todo_app()
            break
        elif choice == "2":
            await develop_blog_system()
            break
        elif choice == "3":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")


if __name__ == "__main__":
    asyncio.run(main())
