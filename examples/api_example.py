#!/usr/bin/env python3
"""
API服务开发示例
演示如何使用AutoGen工作流开发API服务
"""

import asyncio
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from enhanced_workflow import EnhancedAppDevWorkflow


async def develop_user_auth_api():
    """开发用户认证API示例"""
    
    requirement = """
开发一个完整的用户认证API服务，具体要求：

## 功能需求
1. **用户注册**
   - 邮箱/用户名注册
   - 密码强度验证
   - 邮箱验证
   - 防重复注册

2. **用户登录**
   - 多种登录方式（邮箱/用户名）
   - JWT token生成
   - 登录失败限制
   - 记住登录状态

3. **用户管理**
   - 用户信息查询
   - 用户信息更新
   - 密码修改
   - 账户注销

4. **安全功能**
   - 密码加密存储
   - JWT token验证
   - 刷新token机制
   - 登录日志记录

## 技术要求
- RESTful API设计
- 数据库设计（用户表、登录日志表）
- 错误处理和状态码
- API文档生成
- 单元测试
- 安全性最佳实践
"""
    
    print("🚀 开始开发用户认证API")
    print("=" * 60)
    
    try:
        # 创建工作流实例
        workflow = EnhancedAppDevWorkflow()
        
        # 运行工作流
        result = await workflow.run_workflow(
            requirement=requirement,
            app_type="api"
        )
        
        if result["success"]:
            print("✅ 用户认证API开发完成！")
            print(f"⏱️  执行时间: {result['duration']:.2f}秒")
        else:
            print(f"❌ 开发失败: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ 执行出错: {str(e)}")


async def develop_ecommerce_api():
    """开发电商API示例"""
    
    requirement = """
开发一个电商平台的核心API服务，具体要求：

## 功能需求
1. **商品管理**
   - 商品CRUD操作
   - 商品分类管理
   - 商品搜索和筛选
   - 商品库存管理

2. **订单管理**
   - 订单创建和查询
   - 订单状态管理
   - 订单支付处理
   - 订单取消和退款

3. **购物车功能**
   - 添加/删除商品
   - 购物车查询
   - 批量操作

4. **用户系统**
   - 用户地址管理
   - 收藏夹功能
   - 购买历史

## 技术要求
- 微服务架构设计
- 数据库设计（商品、订单、用户等表）
- 缓存策略
- 消息队列集成
- API限流和安全
- 完整的错误处理
"""
    
    print("🚀 开始开发电商API")
    print("=" * 60)
    
    try:
        # 创建工作流实例
        workflow = EnhancedAppDevWorkflow()
        
        # 运行工作流
        result = await workflow.run_workflow(
            requirement=requirement,
            app_type="api"
        )
        
        if result["success"]:
            print("✅ 电商API开发完成！")
            print(f"⏱️  执行时间: {result['duration']:.2f}秒")
        else:
            print(f"❌ 开发失败: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ 执行出错: {str(e)}")


async def develop_file_storage_api():
    """开发文件存储API示例"""
    
    requirement = """
开发一个云文件存储API服务，具体要求：

## 功能需求
1. **文件上传**
   - 单文件/多文件上传
   - 大文件分片上传
   - 文件类型验证
   - 文件大小限制

2. **文件管理**
   - 文件列表查询
   - 文件信息获取
   - 文件重命名
   - 文件删除

3. **文件夹管理**
   - 创建/删除文件夹
   - 文件夹移动
   - 文件夹权限管理

4. **分享功能**
   - 生成分享链接
   - 设置分享权限
   - 分享链接过期管理

## 技术要求
- 文件存储策略（本地/云存储）
- 文件元数据管理
- 权限控制系统
- 文件预览功能
- 下载加速
- 数据备份策略
"""
    
    print("🚀 开始开发文件存储API")
    print("=" * 60)
    
    try:
        # 创建工作流实例
        workflow = EnhancedAppDevWorkflow()
        
        # 运行工作流
        result = await workflow.run_workflow(
            requirement=requirement,
            app_type="api"
        )
        
        if result["success"]:
            print("✅ 文件存储API开发完成！")
            print(f"⏱️  执行时间: {result['duration']:.2f}秒")
        else:
            print(f"❌ 开发失败: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ 执行出错: {str(e)}")


async def main():
    """主函数"""
    print("AutoGen API服务开发示例")
    print("=" * 40)
    print("1. 用户认证API")
    print("2. 电商API")
    print("3. 文件存储API")
    print("4. 退出")
    
    while True:
        choice = input("\n请选择示例 (1-4): ").strip()
        
        if choice == "1":
            await develop_user_auth_api()
            break
        elif choice == "2":
            await develop_ecommerce_api()
            break
        elif choice == "3":
            await develop_file_storage_api()
            break
        elif choice == "4":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")


if __name__ == "__main__":
    asyncio.run(main())
