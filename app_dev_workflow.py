#!/usr/bin/env python3
"""
AutoGen APP开发工作流
使用AutoGen框架实现三个agent协作的APP开发流程：
- Agent1: 代码编写者 (Coder)
- Agent2: 代码审查者 (Reviewer) 
- Agent3: 代码优化者 (Optimizer)
"""

import asyncio
import os
import json
from datetime import datetime
from typing import Optional

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.teams import DiGraphBuilder, GraphFlow
from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient


class AppDevWorkflow:
    """APP开发工作流管理器"""
    
    def __init__(self, model_name: str = "gpt-4o", api_key: Optional[str] = None):
        """
        初始化工作流
        
        Args:
            model_name: 使用的模型名称
            api_key: OpenAI API密钥，如果为None则从环境变量获取
        """
        self.model_name = model_name
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        
        if not self.api_key:
            raise ValueError("请设置OPENAI_API_KEY环境变量或提供api_key参数")
        
        # 创建模型客户端
        self.model_client = OpenAIChatCompletionClient(
            model=self.model_name,
            api_key=self.api_key
        )
        
        # 初始化agents
        self.coder_agent = None
        self.reviewer_agent = None
        self.optimizer_agent = None
        self.workflow = None
        
    def _create_agents(self, app_type: str = "web"):
        """创建三个专门的agents"""
        
        # Agent1: 代码编写者
        coder_system_message = f"""你是一个专业的{app_type}应用开发工程师。你的职责是：

1. 根据用户需求编写高质量的代码
2. 遵循最佳实践和编码规范
3. 确保代码的可读性和可维护性
4. 包含必要的注释和文档
5. 考虑性能和安全性

请按照以下格式输出代码：
```[语言]
[代码内容]
```

在代码后添加简要说明，解释主要功能和设计思路。
完成后请说"CODE_WRITTEN"。
"""

        self.coder_agent = AssistantAgent(
            name="coder",
            model_client=self.model_client,
            system_message=coder_system_message,
            description="专业的代码编写工程师，负责根据需求编写高质量代码"
        )
        
        # Agent2: 代码审查者
        reviewer_system_message = """你是一个资深的代码审查专家。你的职责是：

1. 仔细审查提供的代码
2. 检查代码质量、安全性、性能问题
3. 验证是否符合最佳实践
4. 检查代码逻辑和潜在bug
5. 提出具体的改进建议

请按照以下格式提供审查报告：

## 代码审查报告

### 优点：
- [列出代码的优点]

### 问题和建议：
- [具体问题1] - 建议：[改进方案]
- [具体问题2] - 建议：[改进方案]

### 总体评价：
[整体评价和建议]

完成审查后请说"REVIEW_COMPLETED"。
"""

        self.reviewer_agent = AssistantAgent(
            name="reviewer",
            model_client=self.model_client,
            system_message=reviewer_system_message,
            description="资深代码审查专家，负责检查代码质量并提出改进建议"
        )
        
        # Agent3: 代码优化者
        optimizer_system_message = """你是一个代码优化专家。你的职责是：

1. 分析原始代码和审查建议
2. 根据审查意见优化代码
3. 提高代码质量、性能和可维护性
4. 确保优化后的代码更加完善
5. 保持原有功能的完整性

请按照以下格式输出优化后的代码：

## 优化后的代码

```[语言]
[优化后的代码内容]
```

## 优化说明
- [优化点1]：[具体改进]
- [优化点2]：[具体改进]

## 总结
[优化总结和最终建议]

完成优化后请说"WORKFLOW_COMPLETE"。
"""

        self.optimizer_agent = AssistantAgent(
            name="optimizer",
            model_client=self.model_client,
            system_message=optimizer_system_message,
            description="代码优化专家，负责根据审查建议优化和完善代码"
        )
    
    def _create_workflow(self):
        """创建工作流图"""
        # 使用DiGraphBuilder创建有向图
        builder = DiGraphBuilder()
        
        # 添加节点
        builder.add_node(self.coder_agent)
        builder.add_node(self.reviewer_agent)
        builder.add_node(self.optimizer_agent)
        
        # 定义工作流：Coder -> Reviewer -> Optimizer
        builder.add_edge(self.coder_agent, self.reviewer_agent)
        builder.add_edge(self.reviewer_agent, self.optimizer_agent)
        
        # 构建图
        graph = builder.build()
        
        # 创建工作流
        self.workflow = GraphFlow(
            participants=[self.coder_agent, self.reviewer_agent, self.optimizer_agent],
            graph=graph
        )
    
    async def run_workflow(self, requirement: str, app_type: str = "web", save_output: bool = True):
        """
        运行APP开发工作流
        
        Args:
            requirement: 应用开发需求描述
            app_type: 应用类型 (web, mobile, desktop)
            save_output: 是否保存输出结果
        """
        print(f"🚀 启动APP开发工作流")
        print(f"📋 需求: {requirement}")
        print(f"🎯 应用类型: {app_type}")
        print("=" * 60)
        
        try:
            # 创建agents和工作流
            self._create_agents(app_type)
            self._create_workflow()
            
            # 构建任务描述
            task = f"""
请开发一个{app_type}应用，具体需求如下：

{requirement}

请按照以下流程进行：
1. Coder: 编写代码实现需求
2. Reviewer: 审查代码并提出改进建议  
3. Optimizer: 根据审查建议优化代码

开始工作流程！
"""
            
            # 运行工作流
            print("🔄 开始执行工作流...")
            result = await Console(self.workflow.run_stream(task=task))
            
            # 保存结果
            if save_output:
                await self._save_workflow_result(requirement, app_type, result)
            
            print("\n✅ 工作流执行完成！")
            return result
            
        except Exception as e:
            print(f"❌ 工作流执行出错: {str(e)}")
            raise
        finally:
            # 清理资源
            await self.cleanup()
    
    async def _save_workflow_result(self, requirement: str, app_type: str, result):
        """保存工作流结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"app_dev_result_{timestamp}.md"
        
        content = f"""# APP开发工作流结果

## 基本信息
- 时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- 应用类型: {app_type}
- 模型: {self.model_name}

## 需求描述
{requirement}

## 工作流执行结果
{result}

---
*由AutoGen APP开发工作流自动生成*
"""
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"📄 结果已保存到: {filename}")
    
    async def cleanup(self):
        """清理资源"""
        if self.model_client:
            await self.model_client.close()


async def main():
    """主函数示例"""
    # 示例需求
    requirement = """
开发一个简单的待办事项管理应用，功能包括：
1. 添加新的待办事项
2. 标记事项为完成状态
3. 删除事项
4. 显示所有事项列表
5. 简洁美观的用户界面
"""
    
    try:
        # 创建工作流实例
        workflow = AppDevWorkflow(model_name="gpt-4o")
        
        # 运行工作流
        await workflow.run_workflow(
            requirement=requirement,
            app_type="web",
            save_output=True
        )
        
    except Exception as e:
        print(f"执行失败: {str(e)}")


if __name__ == "__main__":
    # 运行主函数
    asyncio.run(main())
