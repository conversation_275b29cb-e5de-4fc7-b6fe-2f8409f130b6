#!/usr/bin/env python3
"""
AutoGen APP开发工作流测试脚本
用于验证工作流的基本功能
"""

import asyncio
import os
import sys
from unittest.mock import AsyncMock, MagicMock

# 模拟测试环境（如果没有API密钥）
def setup_test_environment():
    """设置测试环境"""
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠️  未检测到OPENAI_API_KEY，使用模拟模式测试")
        os.environ["OPENAI_API_KEY"] = "test-key-for-validation"
        return True
    return False


async def test_config_validation():
    """测试配置验证"""
    print("🧪 测试配置验证...")
    
    try:
        from config import WorkflowConfig, EnvConfig, PromptTemplates
        
        # 测试模型验证
        assert WorkflowConfig.validate_model("gpt-4o") == True
        assert WorkflowConfig.validate_model("invalid-model") == False
        
        # 测试应用类型验证
        assert WorkflowConfig.validate_app_type("web") == True
        assert WorkflowConfig.validate_app_type("invalid-type") == False
        
        # 测试提示词模板
        prompt = PromptTemplates.get_coder_prompt("web", ["React", "JavaScript"])
        assert "web" in prompt
        assert "React" in prompt
        
        print("✅ 配置验证测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置验证测试失败: {str(e)}")
        return False


async def test_workflow_creation():
    """测试工作流创建"""
    print("🧪 测试工作流创建...")
    
    try:
        from enhanced_workflow import EnhancedAppDevWorkflow
        
        # 创建工作流实例
        workflow = EnhancedAppDevWorkflow()
        
        # 测试agents创建
        workflow._create_agents("web")
        assert len(workflow.agents) == 3
        assert "coder" in workflow.agents
        assert "reviewer" in workflow.agents
        assert "optimizer" in workflow.agents
        
        # 测试工作流图创建
        workflow._create_workflow()
        assert workflow.workflow is not None
        
        print("✅ 工作流创建测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 工作流创建测试失败: {str(e)}")
        return False


async def test_cli_parser():
    """测试命令行解析器"""
    print("🧪 测试命令行解析器...")
    
    try:
        from cli import WorkflowCLI
        
        cli = WorkflowCLI()
        parser = cli.create_parser()
        
        # 测试基本参数解析
        args = parser.parse_args([
            "--requirement", "test requirement",
            "--app-type", "web",
            "--model", "gpt-4o"
        ])
        
        assert args.requirement == "test requirement"
        assert args.app_type == "web"
        assert args.model == "gpt-4o"
        
        print("✅ 命令行解析器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 命令行解析器测试失败: {str(e)}")
        return False


async def test_mock_workflow():
    """测试模拟工作流执行"""
    print("🧪 测试模拟工作流执行...")
    
    try:
        # 这里可以添加模拟的工作流执行测试
        # 由于需要真实的API调用，我们只测试基本的初始化
        
        from enhanced_workflow import EnhancedAppDevWorkflow
        
        # 创建工作流实例
        workflow = EnhancedAppDevWorkflow()
        
        # 测试任务描述构建
        from config import WorkflowConfig
        app_info = WorkflowConfig.get_app_type_info("web")
        task = workflow._build_task_description(
            "测试需求", 
            "web", 
            app_info
        )
        
        assert "测试需求" in task
        assert "web" in task.lower()
        
        print("✅ 模拟工作流执行测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 模拟工作流执行测试失败: {str(e)}")
        return False


async def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行AutoGen工作流测试")
    print("=" * 50)
    
    # 设置测试环境
    is_mock_mode = setup_test_environment()
    
    tests = [
        ("配置验证", test_config_validation),
        ("工作流创建", test_workflow_creation),
        ("命令行解析器", test_cli_parser),
        ("模拟工作流执行", test_mock_workflow),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 运行测试: {test_name}")
        try:
            result = await test_func()
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_name} 出现异常: {str(e)}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        if is_mock_mode:
            print("💡 提示: 设置真实的OPENAI_API_KEY来测试完整功能")
    else:
        print("⚠️  部分测试失败，请检查代码")
    
    return passed == total


async def demo_workflow():
    """演示工作流（如果有API密钥）"""
    if not os.getenv("OPENAI_API_KEY") or os.getenv("OPENAI_API_KEY") == "test-key-for-validation":
        print("⚠️  跳过演示：需要真实的OPENAI_API_KEY")
        return
    
    print("\n🎬 运行工作流演示...")
    
    try:
        from enhanced_workflow import EnhancedAppDevWorkflow
        
        workflow = EnhancedAppDevWorkflow()
        
        # 简单的演示需求
        requirement = "创建一个简单的Hello World网页，包含标题和欢迎信息"
        
        result = await workflow.run_workflow(
            requirement=requirement,
            app_type="web"
        )
        
        if result["success"]:
            print("✅ 演示工作流执行成功！")
            print(f"⏱️  执行时间: {result['duration']:.2f}秒")
        else:
            print(f"❌ 演示工作流执行失败: {result.get('error')}")
    
    except Exception as e:
        print(f"❌ 演示执行出错: {str(e)}")


async def main():
    """主函数"""
    # 运行测试
    test_passed = await run_all_tests()
    
    # 如果测试通过且有API密钥，运行演示
    if test_passed:
        await demo_workflow()
    
    print("\n🏁 测试完成")


if __name__ == "__main__":
    asyncio.run(main())
